// SPDX-License-Identifier: GPL-2.0-or-later
// Copyright (c) 2025 Morpho Association
pragma solidity ^0.8.0;

import "./BaseTest.sol";

/**
 * @title Permanent Deposit Blocking Vulnerability POC
 * @notice This POC demonstrates that the VaultV2 contract implements a one-way circuit breaker
 *         that permanently blocks deposits after any loss realization, making the vault unusable.
 *
 * VULNERABILITY SUMMARY:
 * - When realizeLoss() is called with loss > 0, enterBlocked is set to true permanently
 * - No mechanism exists to reset enterBlocked back to false
 * - All future deposits and mints are blocked forever
 * - Even governance/admin functions cannot restore deposit functionality
 *
 * ATTACK FLOW:
 * 1. Vault operates normally with deposits and allocations
 * 2. Any loss occurs in an adapter (natural or malicious)
 * 3. realizeLoss() is called, setting enterBlocked = true
 * 4. All future deposits are permanently blocked
 * 5. Vault becomes unusable for new capital
 */
contract PermanentDepositBlockingPOC is BaseTest {
    AdapterMock internal adapter;
    uint256 constant INITIAL_DEPOSIT = 1000e18;
    uint256 constant LOSS_AMOUNT = 100e18;
    uint256 constant ATTEMPTED_DEPOSIT = 500e18;

    function setUp() public override {
        super.setUp();

        adapter = new AdapterMock(address(vault));

        // Setup adapter permissions
        vm.prank(curator);
        vault.submit(abi.encodeCall(IVaultV2.setIsAdapter, (address(adapter), true)));
        vault.setIsAdapter(address(adapter), true);

        // Setup test tokens and approvals
        deal(address(underlyingToken), address(this), type(uint256).max);
        underlyingToken.approve(address(vault), type(uint256).max);

        // Setup caps for allocations
        increaseAbsoluteCap(expectedIdData[0], type(uint128).max);
        increaseAbsoluteCap(expectedIdData[1], type(uint128).max);
        increaseRelativeCap(expectedIdData[0], WAD);
        increaseRelativeCap(expectedIdData[1], WAD);
    }

    /**
     * @notice Comprehensive test demonstrating the permanent deposit blocking vulnerability
     */
    function testPermanentDepositBlockingVulnerability() public {
        // ========== PHASE 1: NORMAL OPERATION ==========
        console.log("=== PHASE 1: NORMAL OPERATION ===");

        // Initial deposit works fine
        uint256 sharesBefore = vault.balanceOf(address(this));
        vault.deposit(INITIAL_DEPOSIT, address(this));
        uint256 sharesAfter = vault.balanceOf(address(this));

        console.log("Initial deposit successful:");
        console.log("- Deposited:", INITIAL_DEPOSIT);
        console.log("- Shares received:", sharesAfter - sharesBefore);
        console.log("- Total assets:", vault.totalAssets());
        console.log("- Enter blocked:", vault.enterBlocked());

        assertEq(vault.totalAssets(), INITIAL_DEPOSIT, "Initial deposit should work");
        assertFalse(vault.enterBlocked(), "Enter should not be blocked initially");

        // Allocate funds to adapter
        vm.prank(allocator);
        vault.allocate(address(adapter), hex"", INITIAL_DEPOSIT);
        console.log("- Allocated to adapter:", INITIAL_DEPOSIT);

        // ========== PHASE 2: LOSS REALIZATION ==========
        console.log("\n=== PHASE 2: LOSS REALIZATION ===");

        // Simulate a loss in the adapter
        adapter.setLoss(LOSS_AMOUNT);
        console.log("Simulated loss in adapter:", LOSS_AMOUNT);

        // Account the loss through allocation
        vm.prank(allocator);
        vault.allocate(address(adapter), hex"", 0);

        // Realize the loss - this triggers the vulnerability
        console.log("Calling realizeLoss...");
        (uint256 incentiveShares, uint256 actualLoss) = vault.realizeLoss(address(adapter), hex"");

        console.log("Loss realization results:");
        console.log("- Actual loss:", actualLoss);
        console.log("- Incentive shares:", incentiveShares);
        console.log("- Total assets after loss:", vault.totalAssets());
        console.log("- Enter blocked:", vault.enterBlocked());

        // Verify the vulnerability is triggered
        assertEq(actualLoss, LOSS_AMOUNT, "Loss should be realized");
        assertEq(vault.totalAssets(), INITIAL_DEPOSIT - LOSS_AMOUNT, "Assets should decrease by loss");
        assertTrue(vault.enterBlocked(), "VULNERABILITY: Enter should be permanently blocked");

        // ========== PHASE 3: PERMANENT BLOCKING DEMONSTRATION ==========
        console.log("\n=== PHASE 3: PERMANENT BLOCKING DEMONSTRATION ===");

        // Attempt to deposit - should fail permanently
        console.log("Attempting new deposit of", ATTEMPTED_DEPOSIT, "...");
        vm.expectRevert(ErrorsLib.EnterBlocked.selector);
        vault.deposit(ATTEMPTED_DEPOSIT, address(this));
        console.log("Deposit blocked as expected");

        // Attempt to mint - should also fail permanently
        console.log("Attempting to mint shares...");
        vm.expectRevert(ErrorsLib.EnterBlocked.selector);
        vault.mint(100e18, address(this));
        console.log("Mint blocked as expected");

        // ========== PHASE 4: BYPASS ATTEMPTS ==========
        console.log("\n=== PHASE 4: BYPASS ATTEMPTS ===");

        // Try different users - should still fail
        address newUser = makeAddr("newUser");
        deal(address(underlyingToken), newUser, ATTEMPTED_DEPOSIT);
        vm.startPrank(newUser);
        underlyingToken.approve(address(vault), ATTEMPTED_DEPOSIT);
        vm.expectRevert(ErrorsLib.EnterBlocked.selector);
        vault.deposit(ATTEMPTED_DEPOSIT, newUser);
        vm.stopPrank();
        console.log(" Different user also blocked");

        // Try after time passes - should still fail
        vm.warp(block.timestamp + 365 days);
        vm.expectRevert(ErrorsLib.EnterBlocked.selector);
        vault.deposit(ATTEMPTED_DEPOSIT, address(this));
        console.log(" Still blocked after 1 year");

        // ========== PHASE 5: GOVERNANCE BYPASS ATTEMPTS ==========
        console.log("\n=== PHASE 5: GOVERNANCE BYPASS ATTEMPTS ===");

        // Try owner functions - none can reset enterBlocked
        vm.startPrank(owner);
        // Owner can change curator, but that doesn't help
        address newCurator = makeAddr("newCurator");
        vault.setCurator(newCurator);
        console.log("Owner changed curator, but enterBlocked still true:", vault.enterBlocked());
        vm.stopPrank();

        // Try curator functions - none can reset enterBlocked
        vm.startPrank(newCurator);
        // Curator can set various parameters but not enterBlocked
        address newAllocator = makeAddr("newAllocator");
        vault.submit(abi.encodeCall(IVaultV2.setIsAllocator, (newAllocator, true)));
        vault.setIsAllocator(newAllocator, true);
        console.log(" Curator changed allocator, but enterBlocked still true:", vault.enterBlocked());
        vm.stopPrank();

        // Verify deposits are still blocked after governance changes
        vm.expectRevert(ErrorsLib.EnterBlocked.selector);
        vault.deposit(ATTEMPTED_DEPOSIT, address(this));
        console.log(" Deposits still blocked after governance changes");

        // ========== PHASE 6: FINAL VERIFICATION ==========
        console.log("\n=== PHASE 6: FINAL VERIFICATION ===");

        // Verify the vault is permanently unusable for new deposits
        assertTrue(vault.enterBlocked(), "CRITICAL: enterBlocked is permanently true");
        console.log("VULNERABILITY CONFIRMED:");
        console.log("- enterBlocked is permanently set to true");
        console.log("- No function exists to reset enterBlocked to false");
        console.log("- All future deposits and mints are permanently blocked");
        console.log("- Vault is effectively dead for new capital");

        // Existing users can still withdraw (this is expected)
        uint256 withdrawAmount = vault.balanceOf(address(this)) / 2;
        vault.redeem(withdrawAmount, address(this), address(this));
        console.log(" Existing users can still withdraw:", withdrawAmount);

        // But new deposits are still impossible
        vm.expectRevert(ErrorsLib.EnterBlocked.selector);
        vault.deposit(1, address(this));
        console.log(" New deposits still impossible even after withdrawals");
    }

    /**
     * @notice Test that demonstrates the transient nature doesn't help
     */
    function testTransientNatureDoesntHelp() public {
        console.log("=== TESTING TRANSIENT NATURE ===");

        // Make initial deposit and realize loss
        vault.deposit(INITIAL_DEPOSIT, address(this));
        vm.prank(allocator);
        vault.allocate(address(adapter), hex"", INITIAL_DEPOSIT);
        adapter.setLoss(LOSS_AMOUNT);
        vm.prank(allocator);
        vault.allocate(address(adapter), hex"", 0);
        vault.realizeLoss(address(adapter), hex"");

        assertTrue(vault.enterBlocked(), "Enter should be blocked");

        // The transient keyword only prevents intra-transaction manipulation
        // It doesn't reset between transactions as one might expect

        // Start a new transaction (new block)
        vm.roll(block.number + 1);
        vm.warp(block.timestamp + 12);

        // enterBlocked is still true in the new transaction
        assertTrue(vault.enterBlocked(), "CRITICAL: enterBlocked persists across transactions");

        // Deposits still fail in new transaction
        vm.expectRevert(ErrorsLib.EnterBlocked.selector);
        vault.deposit(1, address(this));

        console.log(" Transient keyword doesn't reset enterBlocked between transactions");
        console.log(" The vulnerability persists across all future transactions");
    }

    
    /**
     * @notice Test impact measurement - quantify the damage
     */
    function testImpactMeasurement() public {
        console.log("=== MEASURING IMPACT ===");

        uint256 totalDepositors = 10;
        uint256 depositPerUser = 100e18;
        address[] memory users = new address[](totalDepositors);

        // Setup multiple users with deposits
        for (uint256 i = 0; i < totalDepositors; i++) {
            users[i] = makeAddr(string(abi.encodePacked("user", i)));
            deal(address(underlyingToken), users[i], depositPerUser * 2);
            vm.startPrank(users[i]);
            underlyingToken.approve(address(vault), depositPerUser * 2);
            vault.deposit(depositPerUser, users[i]);
            vm.stopPrank();
        }

        uint256 totalAssetsBeforeLoss = vault.totalAssets();
        console.log("Total assets before loss:", totalAssetsBeforeLoss);
        console.log("Number of depositors:", totalDepositors);

        // Allocate and realize loss
        vm.prank(allocator);
        vault.allocate(address(adapter), hex"", totalAssetsBeforeLoss);
        adapter.setLoss(LOSS_AMOUNT);
        vm.prank(allocator);
        vault.allocate(address(adapter), hex"", 0);
        vault.realizeLoss(address(adapter), hex"");

        uint256 totalAssetsAfterLoss = vault.totalAssets();
        console.log("Total assets after loss:", totalAssetsAfterLoss);
        console.log("Loss amount:", LOSS_AMOUNT);
        console.log("Loss percentage:", (LOSS_AMOUNT * 100) / totalAssetsBeforeLoss, "%");

        // Measure impact: no new deposits possible
        uint256 blockedCapital = 0;
        for (uint256 i = 0; i < totalDepositors; i++) {
            vm.startPrank(users[i]);
            vm.expectRevert(ErrorsLib.EnterBlocked.selector);
            vault.deposit(depositPerUser, users[i]);
            blockedCapital += depositPerUser;
            vm.stopPrank();
        }

        console.log("IMPACT MEASUREMENT:");
        console.log("- Blocked future capital:", blockedCapital);
        console.log("- Vault is permanently unusable for new deposits");
        console.log("- Loss of", (LOSS_AMOUNT * 100) / totalAssetsBeforeLoss, "% causes 100% deposit blocking");
        console.log("- Economic impact: Infinite (vault becomes unusable)");
    }

    
}